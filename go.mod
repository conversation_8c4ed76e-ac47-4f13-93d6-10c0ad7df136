module github.com/nrf110/connectrpc-permify-propelauth

go 1.24.0

require (
	connectrpc.com/connect v1.18.1
	github.com/google/uuid v1.3.0
	github.com/nrf110/connectrpc-permify v0.4.0
	github.com/ovechkin-dm/mockio v1.0.2
	github.com/propelauth/propelauth-go v0.26.2
	github.com/stretchr/testify v1.11.1
)

require (
	buf.build/gen/go/envoyproxy/protoc-gen-validate/protocolbuffers/go v1.36.8-20240617172848-daf171c6cdb5.1 // indirect
	buf.build/gen/go/grpc-ecosystem/grpc-gateway/protocolbuffers/go v1.36.8-20241220201140-4c5ba75caaf8.1 // indirect
	buf.build/gen/go/permifyco/permify/protocolbuffers/go v1.36.8-20250821104952-d45a0df11d45.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/ovechkin-dm/go-dyno v0.5.3 // indirect
	github.com/petermattis/goid v0.0.0-20250813065127-a731cc31b4fe // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250826171959-ef028d996bc1 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250826171959-ef028d996bc1 // indirect
	google.golang.org/protobuf v1.36.8 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
